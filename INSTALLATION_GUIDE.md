# AST Analysis Plugin - 详细安装和使用指南

## 目录
1. [系统要求](#系统要求)
2. [编译步骤](#编译步骤)
3. [安装方法](#安装方法)
4. [使用教程](#使用教程)
5. [故障排除](#故障排除)
6. [高级配置](#高级配置)

## 系统要求

### 必需软件
- **IntelliJ IDEA**: 2023.1 或更高版本 (Community或Ultimate版本)
- **Java**: JDK 17 或更高版本
- **操作系统**: Windows 10+, macOS 10.14+, 或 Linux (Ubuntu 18.04+)

### 推荐配置
- **内存**: 8GB RAM 或更多
- **存储**: 至少500MB可用空间
- **网络**: 稳定的互联网连接（用于下载依赖）

### 必需的IntelliJ插件
确保以下插件已安装并启用：
- **Java** (通常默认安装)
- **Python** (如需分析Python文件)
- **JavaScript and TypeScript** (如需分析JavaScript文件)

## 编译步骤

### 1. 准备开发环境

#### 安装Java 17
```bash
# 检查当前Java版本
java -version

# 如果版本低于17，请下载并安装Java 17
# 从 https://adoptium.net/ 下载
```

#### 验证Gradle
项目使用Gradle Wrapper，无需单独安装Gradle。

### 2. 获取源代码

```bash
# 克隆项目（如果使用Git）
git clone <repository-url>
cd demo_plugins

# 或者直接下载并解压源代码包
```

### 3. 编译项目

#### 基本编译
```bash
# 在项目根目录执行
./gradlew build

# Windows用户使用
gradlew.bat build
```

#### 生成插件包
```bash
# 生成可安装的插件包
./gradlew buildPlugin

# 插件包将生成在 build/distributions/ 目录
```

#### 清理和重新编译
```bash
# 清理之前的编译结果
./gradlew clean

# 重新编译
./gradlew clean build
```

### 4. 编译验证

编译成功后，检查以下文件是否存在：
- `build/distributions/AST Analysis Plugin-1.0-SNAPSHOT.zip`
- `build/libs/demo_plugins-1.0-SNAPSHOT.jar`

## 安装方法

### 方法一：从编译包安装（推荐）

1. **编译插件包**
   ```bash
   ./gradlew buildPlugin
   ```

2. **打开IntelliJ IDEA**

3. **进入插件设置**
   - 菜单: `File` → `Settings` (Windows/Linux) 或 `IntelliJ IDEA` → `Preferences` (macOS)
   - 左侧选择: `Plugins`

4. **安装插件**
   - 点击齿轮图标 ⚙️
   - 选择 `Install Plugin from Disk...`
   - 浏览到 `build/distributions/AST Analysis Plugin-1.0-SNAPSHOT.zip`
   - 点击 `OK`

5. **重启IDE**
   - 点击 `Restart IDE` 按钮
   - 或手动重启IntelliJ IDEA

### 方法二：开发模式运行

适用于开发和测试：

```bash
# 启动包含插件的IntelliJ IDEA实例
./gradlew runIde
```

这将启动一个新的IntelliJ IDEA窗口，插件已自动加载。

### 方法三：调试模式

```bash
# 以调试模式启动
./gradlew runIde --debug-jvm
```

然后在IDE中连接到调试端口（通常是5005）。

## 使用教程

### 基本操作

#### 1. 生成AST报告

**方法一：使用快捷键**
1. 在编辑器中打开Java、Python或JavaScript文件
2. 按下 `Ctrl+Alt+A` (Windows/Linux) 或 `Cmd+Alt+A` (macOS)
3. 报告将在弹出窗口中显示

**方法二：使用菜单**
1. 打开要分析的文件
2. 菜单: `Code` → `Generate AST Report`
3. 查看生成的报告

#### 2. 导出AST报告

**方法一：使用快捷键**
1. 打开要分析的文件
2. 按下 `Ctrl+Alt+E` (Windows/Linux) 或 `Cmd+Alt+E` (macOS)
3. 选择保存目录
4. 报告将保存为文本文件

**方法二：使用菜单**
1. 打开要分析的文件
2. 菜单: `Code` → `Export AST Report`
3. 选择保存位置

### 支持的文件类型和分析内容

#### Java文件 (.java)
- ✅ 包声明
- ✅ 导入语句
- ✅ 类定义（修饰符、继承、接口）
- ✅ 字段信息
- ✅ 方法签名
- ✅ 方法调用关系

#### Python文件 (.py)
- ✅ 类定义
- ✅ 继承关系
- ✅ 类方法
- ✅ 顶级函数
- ✅ 函数参数

#### JavaScript文件 (.js)
- ✅ 函数定义
- ✅ 变量声明
- ✅ 函数参数

### 示例文件

项目包含示例文件用于测试：
- `examples/SampleJavaFile.java` - Java示例
- `examples/sample_python_file.py` - Python示例
- `examples/sample_javascript_file.js` - JavaScript示例

### 报告格式说明

生成的报告包含以下信息：
```
=== AST Report ===
File: [文件名]
Language: [编程语言]
Project: [项目名]
========================================

[详细的AST结构信息]
```

导出的报告还包含：
- 生成时间戳
- 文件路径信息
- 格式化的结构数据

## 故障排除

### 常见问题

#### 1. 插件无法加载
**症状**: 插件安装后不出现在菜单中

**解决方案**:
- 检查IntelliJ IDEA版本 (需要2023.1+)
- 确认插件已正确安装并启用
- 重启IDE
- 检查IDE日志文件

#### 2. 编译失败
**症状**: `./gradlew build` 失败

**解决方案**:
```bash
# 检查Java版本
java -version

# 清理并重新编译
./gradlew clean build --refresh-dependencies

# 检查网络连接
./gradlew build --info
```

#### 3. 分析结果为空
**症状**: 生成的报告没有内容

**解决方案**:
- 确认文件类型受支持
- 检查文件是否包含有效代码
- 确认相关语言插件已安装

#### 4. 导出失败
**症状**: 无法保存报告文件

**解决方案**:
- 检查目标目录写入权限
- 确认磁盘空间充足
- 尝试选择不同的保存位置

### 日志查看

#### 查看IDE日志
1. 菜单: `Help` → `Show Log in Explorer/Finder`
2. 打开 `idea.log` 文件
3. 搜索 "astplugin" 或错误信息

#### 启用调试日志
在 `Help` → `Diagnostic Tools` → `Debug Log Settings` 中添加：
```
com.deepcode.astplugin
```

### 性能优化

#### 大文件处理
- 对于大型文件，分析可能需要较长时间
- 建议关闭其他不必要的插件
- 增加IDE内存分配

#### 内存设置
在 `Help` → `Edit Custom VM Options` 中调整：
```
-Xmx4g
-XX:MaxMetaspaceSize=512m
```

## 高级配置

### 自定义快捷键

1. 进入 `File` → `Settings` → `Keymap`
2. 搜索 "AST Report"
3. 右键点击动作，选择 `Add Keyboard Shortcut`
4. 设置自定义快捷键

### 扩展功能

#### 添加新语言支持
1. 修改 `ASTAnalyzer.java`
2. 添加语言检测逻辑
3. 实现分析方法
4. 更新 `plugin.xml` 依赖

#### 自定义报告格式
修改 `ASTAnalyzer.java` 中的格式化方法：
- `appendSection()`
- `appendLine()`

### 开发环境设置

#### IDE配置
1. 导入项目到IntelliJ IDEA
2. 设置Project SDK为Java 17
3. 配置代码风格和格式化规则

#### 调试配置
1. 创建新的Run Configuration
2. 类型选择 "Gradle"
3. 任务设置为 "runIde"
4. 启用调试模式

---

如有其他问题，请查看 [README.md](README.md) 或联系技术支持。
