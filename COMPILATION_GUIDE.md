# AST Analysis Plugin - 编译指南

## 重要说明

由于IntelliJ IDEA插件开发的要求，本项目需要 **Java 11 或更高版本** 才能编译。如果您当前使用的是Java 8，请按照以下步骤升级Java版本。

## 系统要求

### 必需软件
- **Java**: JDK 11 或更高版本 (推荐 JDK 17)
- **IntelliJ IDEA**: 2021.3 或更高版本
- **操作系统**: Windows 10+, macOS 10.14+, 或 Linux (Ubuntu 18.04+)

## Java版本升级指南

### 检查当前Java版本
```bash
java -version
javac -version
```

### 安装Java 17 (推荐)

#### macOS
```bash
# 使用Homebrew
brew install openjdk@17

# 或者从官网下载
# https://adoptium.net/temurin/releases/
```

#### Windows
1. 访问 https://adoptium.net/temurin/releases/
2. 下载 Java 17 LTS 版本
3. 运行安装程序
4. 设置环境变量 JAVA_HOME

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install openjdk-17-jdk

# 设置默认Java版本
sudo update-alternatives --config java
```

### 设置环境变量

#### macOS/Linux
在 `~/.bashrc` 或 `~/.zshrc` 中添加：
```bash
export JAVA_HOME=/path/to/java17
export PATH=$JAVA_HOME/bin:$PATH
```

#### Windows
1. 右键 "此电脑" → "属性" → "高级系统设置"
2. 点击 "环境变量"
3. 新建系统变量 `JAVA_HOME`，值为Java安装路径
4. 编辑 `PATH` 变量，添加 `%JAVA_HOME%\bin`

## 编译步骤

### 1. 验证Java版本
```bash
java -version
# 应该显示 Java 11 或更高版本
```

### 2. 克隆项目
```bash
git clone <repository-url>
cd demo_plugins
```

### 3. 编译项目
```bash
# 清理并编译
./gradlew clean build

# Windows用户使用
gradlew.bat clean build
```

### 4. 生成插件包
```bash
# 生成可安装的插件包
./gradlew buildPlugin

# 插件包位置: build/distributions/AST Analysis Plugin-1.0-SNAPSHOT.zip
```

### 5. 运行测试
```bash
# 运行单元测试
./gradlew test

# 运行插件测试环境
./gradlew runIde
```

## 编译配置说明

当前项目配置：
- **Gradle版本**: 7.6
- **IntelliJ插件版本**: 1.5.3
- **目标IntelliJ版本**: 2021.3.3
- **Java版本**: 11 (最低要求)

## 故障排除

### 常见编译错误

#### 1. Java版本不兼容
```
错误: Could not resolve org.jetbrains.intellij.plugins:gradle-intellij-plugin
原因: Java版本低于11
解决: 升级到Java 11或更高版本
```

#### 2. 网络连接问题
```bash
# 使用代理
./gradlew build -Dhttp.proxyHost=proxy.company.com -Dhttp.proxyPort=8080

# 或者配置gradle.properties
echo "systemProp.http.proxyHost=proxy.company.com" >> gradle.properties
echo "systemProp.http.proxyPort=8080" >> gradle.properties
```

#### 3. 内存不足
```bash
# 增加Gradle内存
export GRADLE_OPTS="-Xmx2g -XX:MaxMetaspaceSize=512m"

# 或者在gradle.properties中设置
echo "org.gradle.jvmargs=-Xmx2g -XX:MaxMetaspaceSize=512m" >> gradle.properties
```

### 清理和重新编译
```bash
# 完全清理
./gradlew clean
rm -rf .gradle build

# 刷新依赖
./gradlew build --refresh-dependencies
```

## 开发环境设置

### IntelliJ IDEA配置
1. 打开项目
2. 设置Project SDK为Java 11+
3. 设置Gradle JVM为Java 11+
4. 启用 "Use Gradle from" → "gradle-wrapper.properties file"

### VS Code配置
如果使用VS Code开发：
1. 安装Java Extension Pack
2. 设置java.home为Java 11+路径
3. 配置java.compile.nullAnalysis.mode

## 替代方案

### 如果无法升级Java版本

1. **使用Docker编译**
   ```bash
   # 创建Dockerfile
   FROM openjdk:17-jdk-slim
   WORKDIR /app
   COPY . .
   RUN ./gradlew build
   ```

2. **使用GitHub Actions**
   - Fork项目到GitHub
   - 使用GitHub Actions自动编译
   - 下载编译好的插件包

3. **使用在线IDE**
   - GitPod
   - GitHub Codespaces
   - 这些环境通常预装Java 11+

## 验证编译结果

编译成功后，检查以下文件：
```
build/
├── distributions/
│   └── AST Analysis Plugin-1.0-SNAPSHOT.zip  # 插件安装包
├── libs/
│   └── demo_plugins-1.0-SNAPSHOT.jar         # 编译的JAR文件
└── reports/
    └── tests/                                 # 测试报告
```

## 下一步

编译成功后，请参考 [INSTALLATION_GUIDE.md](INSTALLATION_GUIDE.md) 了解如何安装和使用插件。

---

**注意**: 如果您在企业环境中无法升级Java版本，请联系系统管理员或使用上述替代方案。
