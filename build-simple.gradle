plugins {
    id 'java'
    id 'application'
}

group = 'com.deepcode'
version = '1.0-SNAPSHOT'

java {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}

repositories {
    mavenCentral()
}

dependencies {
    testImplementation 'junit:junit:4.13.2'
}

application {
    mainClass = 'com.deepcode.astplugin.SimpleASTAnalyzer'
}

// Task to create a standalone JAR
task fatJar(type: Jar) {
    archiveBaseName = 'simple-ast-analyzer'
    archiveVersion = '1.0'
    from { configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) } }
    with jar
    manifest {
        attributes 'Main-Class': 'com.deepcode.astplugin.SimpleASTAnalyzer'
    }
}

// Task to run analysis on example files
task analyzeExamples {
    doLast {
        def analyzer = 'com.deepcode.astplugin.SimpleASTAnalyzer'
        def examples = [
            'examples/SampleJavaFile.java',
            'examples/sample_python_file.py',
            'examples/sample_javascript_file.js'
        ]
        
        examples.each { example ->
            if (file(example).exists()) {
                println "Analyzing: $example"
                println "=" * 50
                exec {
                    commandLine 'java', '-cp', sourceSets.main.runtimeClasspath.asPath, analyzer, example
                }
                println "\n"
            }
        }
    }
}

analyzeExamples.dependsOn classes
