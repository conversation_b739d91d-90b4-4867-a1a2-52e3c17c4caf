# AST Analysis Plugin - 完整使用手册

## 目录
1. [快速开始](#快速开始)
2. [独立版本使用](#独立版本使用)
3. [IntelliJ插件版本](#intellij插件版本)
4. [功能演示](#功能演示)
5. [高级用法](#高级用法)
6. [故障排除](#故障排除)

## 快速开始

### 方式一：独立命令行版本（推荐）

**优势**: 无需安装IDE，支持Java 8+，即开即用

```bash
# 1. 确保已安装Java 8或更高版本
java -version

# 2. 下载预编译的JAR文件（已包含在项目中）
# 文件位置: simple-ast-analyzer.jar

# 3. 分析代码文件
java -jar simple-ast-analyzer.jar <文件路径>
```

**示例**:
```bash
# 分析Java文件
java -jar simple-ast-analyzer.jar examples/SampleJavaFile.java

# 分析Python文件
java -jar simple-ast-analyzer.jar examples/sample_python_file.py

# 分析JavaScript文件
java -jar simple-ast-analyzer.jar examples/sample_javascript_file.js
```

### 方式二：IntelliJ IDEA插件版本

**优势**: 完整IDE集成，图形界面，导出功能

**要求**: IntelliJ IDEA 2021.3+, Java 11+

详细安装步骤请参考 [COMPILATION_GUIDE.md](COMPILATION_GUIDE.md)

## 独立版本使用

### 基本用法

```bash
java -jar simple-ast-analyzer.jar <文件路径>
```

### 支持的文件类型

#### Java文件 (.java)
```bash
java -jar simple-ast-analyzer.jar MyClass.java
```

**分析内容**:
- 包声明
- 导入语句
- 类定义和修饰符
- 方法签名
- 基本结构信息

#### Python文件 (.py)
```bash
java -jar simple-ast-analyzer.jar script.py
```

**分析内容**:
- 类定义
- 函数定义
- 导入语句
- 基本结构信息

#### JavaScript文件 (.js)
```bash
java -jar simple-ast-analyzer.jar app.js
```

**分析内容**:
- 函数定义（包括箭头函数）
- 变量声明
- 类定义（ES6）
- 基本结构信息

### 输出示例

#### Java文件分析输出
```
=== Simple AST Analysis Report ===
File: SampleJavaFile.java
Analysis Type: Regex-based parsing
Timestamp: Mon Jun 16 19:38:21 CST 2025
========================================

Java File Analysis:
    Package: com.example.demo
    
    Imports:
        java.util.List
        java.util.ArrayList
        java.util.Map
        java.util.HashMap
    
    Class: SampleJavaFile:
        Modifiers: public
    
    Methods:
        public SampleJavaFile()
        public String getName()
        public int getAge()
        public void setName()
        public void setAge()
        public void addHobby()
        public void removeHobby()
        public void run()
        private void processHobbies()
        public static String getConstant()
        public String toString()
```

### 批量分析

创建脚本进行批量分析：

#### Windows (batch)
```batch
@echo off
for %%f in (*.java) do (
    echo Analyzing %%f
    java -jar simple-ast-analyzer.jar "%%f" > "%%f.analysis.txt"
)
```

#### macOS/Linux (bash)
```bash
#!/bin/bash
for file in *.java; do
    echo "Analyzing $file"
    java -jar simple-ast-analyzer.jar "$file" > "$file.analysis.txt"
done
```

## IntelliJ插件版本

### 安装

1. **编译插件** (需要Java 11+)
   ```bash
   ./gradlew buildPlugin
   ```

2. **安装到IDE**
   - 打开IntelliJ IDEA
   - File → Settings → Plugins
   - 齿轮图标 → Install Plugin from Disk
   - 选择 `build/distributions/AST Analysis Plugin-1.0-SNAPSHOT.zip`

### 使用方法

#### 生成AST报告
1. 在编辑器中打开代码文件
2. 使用快捷键 `Ctrl+Alt+A` (Windows/Linux) 或 `Cmd+Alt+A` (macOS)
3. 或通过菜单: `Code` → `Generate AST Report`

#### 导出AST报告
1. 在编辑器中打开代码文件
2. 使用快捷键 `Ctrl+Alt+E` (Windows/Linux) 或 `Cmd+Alt+E` (macOS)
3. 或通过菜单: `Code` → `Export AST Report`
4. 选择保存目录

## 功能演示

### 示例文件

项目包含三个示例文件用于测试：

1. **examples/SampleJavaFile.java** - Java示例
   - 包含类、方法、字段、继承等
   
2. **examples/sample_python_file.py** - Python示例
   - 包含类、函数、继承、类型注解等
   
3. **examples/sample_javascript_file.js** - JavaScript示例
   - 包含函数、类、变量、箭头函数等

### 运行演示

```bash
# 分析所有示例文件
java -jar simple-ast-analyzer.jar examples/SampleJavaFile.java
java -jar simple-ast-analyzer.jar examples/sample_python_file.py
java -jar simple-ast-analyzer.jar examples/sample_javascript_file.js
```

## 高级用法

### 自定义分析

如果需要自定义分析逻辑，可以修改 `SimpleASTAnalyzer.java`:

1. 编辑源代码
2. 重新编译:
   ```bash
   javac -d build/classes src/main/java/com/deepcode/astplugin/SimpleASTAnalyzer.java
   jar cfm simple-ast-analyzer.jar MANIFEST.MF -C build/classes .
   ```

### 集成到构建流程

#### Maven集成
```xml
<plugin>
    <groupId>org.codehaus.mojo</groupId>
    <artifactId>exec-maven-plugin</artifactId>
    <version>3.1.0</version>
    <executions>
        <execution>
            <phase>verify</phase>
            <goals>
                <goal>java</goal>
            </goals>
            <configuration>
                <mainClass>com.deepcode.astplugin.SimpleASTAnalyzer</mainClass>
                <arguments>
                    <argument>src/main/java/MyClass.java</argument>
                </arguments>
            </configuration>
        </execution>
    </executions>
</plugin>
```

#### Gradle集成
```gradle
task analyzeCode(type: JavaExec) {
    classpath = files('simple-ast-analyzer.jar')
    main = 'com.deepcode.astplugin.SimpleASTAnalyzer'
    args 'src/main/java/MyClass.java'
}
```

### 输出重定向

```bash
# 保存到文件
java -jar simple-ast-analyzer.jar MyClass.java > analysis-report.txt

# 追加到文件
java -jar simple-ast-analyzer.jar MyClass.java >> all-reports.txt

# 同时显示和保存
java -jar simple-ast-analyzer.jar MyClass.java | tee analysis-report.txt
```

## 故障排除

### 常见问题

#### 1. Java版本问题
```
错误: java.lang.UnsupportedClassVersionError
解决: 确保使用Java 8或更高版本
检查: java -version
```

#### 2. 文件路径问题
```
错误: Error reading file: java.nio.file.NoSuchFileException
解决: 检查文件路径是否正确，使用绝对路径或相对路径
示例: java -jar simple-ast-analyzer.jar ./examples/SampleJavaFile.java
```

#### 3. 权限问题
```
错误: java.nio.file.AccessDeniedException
解决: 确保有读取文件的权限
检查: ls -la <文件路径>
```

#### 4. 内存不足
```
错误: java.lang.OutOfMemoryError
解决: 增加JVM内存
使用: java -Xmx512m -jar simple-ast-analyzer.jar <文件路径>
```

### 调试模式

启用详细输出：
```bash
java -Djava.util.logging.level=ALL -jar simple-ast-analyzer.jar <文件路径>
```

### 性能优化

对于大文件：
```bash
# 增加内存
java -Xmx1g -jar simple-ast-analyzer.jar large-file.java

# 使用并行处理（自定义脚本）
find . -name "*.java" -print0 | xargs -0 -P 4 -I {} java -jar simple-ast-analyzer.jar {}
```

## 技术支持

如有问题，请：
1. 查看本文档的故障排除部分
2. 检查 [README.md](README.md) 中的常见问题
3. 查看项目的示例文件和输出
4. 联系技术支持: <EMAIL>

---

**版本**: 1.0  
**最后更新**: 2024年6月
