plugins {
    id("java")
    id("org.jetbrains.intellij") version "1.17.2"
}

group = "com.deepcode"
version = "1.0-SNAPSHOT"

extensions.configure<JavaPluginExtension> {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

extensions.configure<org.jetbrains.intellij.IntelliJPluginExtension> {
    version.set("2022.3.3")
    type.set("IC") // 使用Community版本，更小更快
    plugins.set(listOf("com.intellij.java"))
}

tasks {
    buildSearchableOptions {
        enabled = false
    }

    patchPluginXml {
        sinceBuild.set("223")
        untilBuild.set("233.*")
    }

    runIde {
        // Configure IDEA home directory for running the plugin
        ideDir.set(file("/Applications/IntelliJ IDEA.app"))
        // Uncomment to specify a specific IDE distribution
        // ideDir.set(file("/path/to/your/ide/installation"))
    }

    test {
        useJUnitPlatform()
        testLogging {
            events("PASSED", "SKIPPED", "FAILED")
        }
    }
}

configurations.all {
    exclude(group = "org.jetbrains", module = "annotations")
    exclude(group = "org.junit.jupiter")
}

dependencies {
    // 测试依赖已排除，因为在IntelliJ插件环境中不兼容
}