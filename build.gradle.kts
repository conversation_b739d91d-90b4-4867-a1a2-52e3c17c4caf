plugins {
    id("java")
    id("org.jetbrains.intellij") version "1.5.3"
}

group = "com.deepcode"
version = "1.0-SNAPSHOT"

extensions.configure<JavaPluginExtension> {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}

extensions.configure<org.jetbrains.intellij.IntelliJPluginExtension> {
    version.set("2021.3.3")
    type.set("IC")
    plugins.set(listOf("com.intellij.java", "PythonCore", "JavaScript"))
}

tasks {
    buildSearchableOptions {
        enabled = false
    }

    patchPluginXml {
        sinceBuild.set("231")
        untilBuild.set("241.*")
    }

    runIde {
        // Configure IDEA home directory for running the plugin
        ideDir.set(file("/Applications/IntelliJ IDEA.app"))
        // Uncomment to specify a specific IDE distribution
        // ideDir.set(file("/path/to/your/ide/installation"))
    }

    test {
        useJUnitPlatform()
        testLogging {
            events("PASSED", "SKIPPED", "FAILED")
        }
    }
}

dependencies {
    testImplementation("org.junit.jupiter:junit-jupiter-api:5.9.2")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine:5.9.2")
}
}