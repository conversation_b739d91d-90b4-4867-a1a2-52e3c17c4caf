plugins {
    id("java")
    id("org.jetbrains.intellij") version "1.15.0"
}

group = "com.example"
version = "1.0-SNAPSHOT"

extensions.configure<JavaPluginExtension> {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

extensions.configure<org.jetbrains.intellij.IntelliJPluginExtension> {
    version.set("2023.1.4")
    type.set("IC")
    plugins.set(listOf("com.intellij.java", "Pythonid", "JavaScript"))
}

tasks {
    buildSearchableOptions {
        enabled = false
    }

    patchPluginXml {
        sinceBuild.set("231")
        untilBuild.set("241.*")
    }

    runIde {
        // Configure IDEA home directory for running the plugin
        ideDir.set(file("/Applications/IntelliJ IDEA.app"))
        // Uncomment to specify a specific IDE distribution
        // ideDir.set(file("/path/to/your/ide/installation"))
    }

    test {
        useJUnitPlatform()
        testLogging {
            events("PASSED", "SKIPPED", "FAILED")
        }
    }
}