package com.deepcode.astplugin;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Simplified AST Analyzer that works without IntelliJ PSI API
 * This version uses regex-based parsing for basic code analysis
 */
public class SimpleASTAnalyzer {
    
    private final StringBuilder report = new StringBuilder();
    private int indentLevel = 0;
    
    public String analyzeFile(String filePath) {
        report.setLength(0);
        indentLevel = 0;
        
        try {
            String content = new String(Files.readAllBytes(Paths.get(filePath)));
            String fileName = Paths.get(filePath).getFileName().toString();
            
            appendHeader(fileName);
            
            if (fileName.endsWith(".java")) {
                analyzeJavaContent(content);
            } else if (fileName.endsWith(".py")) {
                analyzePythonContent(content);
            } else if (fileName.endsWith(".js")) {
                analyzeJavaScriptContent(content);
            } else {
                appendLine("Unsupported file type: " + fileName);
            }
            
        } catch (IOException e) {
            appendLine("Error reading file: " + e.getMessage());
        }
        
        return report.toString();
    }
    
    private void appendHeader(String fileName) {
        appendLine("=== Simple AST Analysis Report ===");
        appendLine("File: " + fileName);
        appendLine("Analysis Type: Regex-based parsing");
        appendLine("Timestamp: " + new Date());
        appendLine("========================================");
    }
    
    private void analyzeJavaContent(String content) {
        appendSection("Java File Analysis");
        indentLevel++;
        
        // Extract package
        Pattern packagePattern = Pattern.compile("package\\s+([\\w\\.]+);");
        Matcher packageMatcher = packagePattern.matcher(content);
        if (packageMatcher.find()) {
            appendLine("Package: " + packageMatcher.group(1));
        }
        
        // Extract imports
        Pattern importPattern = Pattern.compile("import\\s+([\\w\\.\\*]+);");
        Matcher importMatcher = importPattern.matcher(content);
        List<String> imports = new ArrayList<>();
        while (importMatcher.find()) {
            imports.add(importMatcher.group(1));
        }
        if (!imports.isEmpty()) {
            appendSection("Imports");
            indentLevel++;
            for (String imp : imports) {
                appendLine(imp);
            }
            indentLevel--;
        }
        
        // Extract classes
        Pattern classPattern = Pattern.compile("(public|private|protected)?\\s*(abstract|final)?\\s*class\\s+(\\w+)");
        Matcher classMatcher = classPattern.matcher(content);
        while (classMatcher.find()) {
            String modifiers = (classMatcher.group(1) != null ? classMatcher.group(1) + " " : "") +
                             (classMatcher.group(2) != null ? classMatcher.group(2) + " " : "");
            appendSection("Class: " + classMatcher.group(3));
            indentLevel++;
            appendLine("Modifiers: " + modifiers.trim());
            indentLevel--;
        }
        
        // Extract methods
        Pattern methodPattern = Pattern.compile("(public|private|protected)?\\s*(static)?\\s*(\\w+)\\s+(\\w+)\\s*\\([^)]*\\)");
        Matcher methodMatcher = methodPattern.matcher(content);
        List<String> methods = new ArrayList<>();
        while (methodMatcher.find()) {
            String modifiers = (methodMatcher.group(1) != null ? methodMatcher.group(1) + " " : "") +
                             (methodMatcher.group(2) != null ? methodMatcher.group(2) + " " : "");
            String returnType = methodMatcher.group(3);
            String methodName = methodMatcher.group(4);
            methods.add(modifiers + returnType + " " + methodName + "()");
        }
        if (!methods.isEmpty()) {
            appendSection("Methods");
            indentLevel++;
            for (String method : methods) {
                appendLine(method);
            }
            indentLevel--;
        }
        
        indentLevel--;
    }
    
    private void analyzePythonContent(String content) {
        appendSection("Python File Analysis");
        indentLevel++;
        
        // Extract classes
        Pattern classPattern = Pattern.compile("class\\s+(\\w+)(?:\\([^)]*\\))?:");
        Matcher classMatcher = classPattern.matcher(content);
        List<String> classes = new ArrayList<>();
        while (classMatcher.find()) {
            classes.add(classMatcher.group(1));
        }
        if (!classes.isEmpty()) {
            appendSection("Classes");
            indentLevel++;
            for (String className : classes) {
                appendLine("Class: " + className);
            }
            indentLevel--;
        }
        
        // Extract functions
        Pattern functionPattern = Pattern.compile("def\\s+(\\w+)\\s*\\([^)]*\\):");
        Matcher functionMatcher = functionPattern.matcher(content);
        List<String> functions = new ArrayList<>();
        while (functionMatcher.find()) {
            functions.add(functionMatcher.group(1));
        }
        if (!functions.isEmpty()) {
            appendSection("Functions");
            indentLevel++;
            for (String funcName : functions) {
                appendLine("Function: " + funcName + "()");
            }
            indentLevel--;
        }
        
        // Extract imports
        Pattern importPattern = Pattern.compile("(?:from\\s+\\w+\\s+)?import\\s+([\\w\\.,\\s]+)");
        Matcher importMatcher = importPattern.matcher(content);
        List<String> imports = new ArrayList<>();
        while (importMatcher.find()) {
            imports.add(importMatcher.group(1).trim());
        }
        if (!imports.isEmpty()) {
            appendSection("Imports");
            indentLevel++;
            for (String imp : imports) {
                appendLine(imp);
            }
            indentLevel--;
        }
        
        indentLevel--;
    }
    
    private void analyzeJavaScriptContent(String content) {
        appendSection("JavaScript File Analysis");
        indentLevel++;
        
        // Extract functions
        Pattern functionPattern = Pattern.compile("function\\s+(\\w+)\\s*\\([^)]*\\)");
        Matcher functionMatcher = functionPattern.matcher(content);
        List<String> functions = new ArrayList<>();
        while (functionMatcher.find()) {
            functions.add(functionMatcher.group(1));
        }
        
        // Extract arrow functions
        Pattern arrowFunctionPattern = Pattern.compile("(?:const|let|var)\\s+(\\w+)\\s*=\\s*\\([^)]*\\)\\s*=>");
        Matcher arrowMatcher = arrowFunctionPattern.matcher(content);
        while (arrowMatcher.find()) {
            functions.add(arrowMatcher.group(1) + " (arrow function)");
        }
        
        if (!functions.isEmpty()) {
            appendSection("Functions");
            indentLevel++;
            for (String funcName : functions) {
                appendLine("Function: " + funcName);
            }
            indentLevel--;
        }
        
        // Extract variables
        Pattern varPattern = Pattern.compile("(?:const|let|var)\\s+(\\w+)");
        Matcher varMatcher = varPattern.matcher(content);
        Set<String> variables = new HashSet<>(); // Use Set to avoid duplicates
        while (varMatcher.find()) {
            variables.add(varMatcher.group(1));
        }
        if (!variables.isEmpty()) {
            appendSection("Variables");
            indentLevel++;
            for (String varName : variables) {
                appendLine("Variable: " + varName);
            }
            indentLevel--;
        }
        
        // Extract classes (ES6)
        Pattern classPattern = Pattern.compile("class\\s+(\\w+)");
        Matcher classMatcher = classPattern.matcher(content);
        List<String> classes = new ArrayList<>();
        while (classMatcher.find()) {
            classes.add(classMatcher.group(1));
        }
        if (!classes.isEmpty()) {
            appendSection("Classes");
            indentLevel++;
            for (String className : classes) {
                appendLine("Class: " + className);
            }
            indentLevel--;
        }
        
        indentLevel--;
    }
    
    private void appendSection(String title) {
        appendLine("");
        appendLine(title + ":");
    }
    
    private void appendLine(String text) {
        report.append("    ".repeat(indentLevel)).append(text).append("\n");
    }
    
    // Main method for standalone testing
    public static void main(String[] args) {
        if (args.length != 1) {
            System.out.println("Usage: java SimpleASTAnalyzer <file-path>");
            return;
        }
        
        SimpleASTAnalyzer analyzer = new SimpleASTAnalyzer();
        String result = analyzer.analyzeFile(args[0]);
        System.out.println(result);
    }
}
