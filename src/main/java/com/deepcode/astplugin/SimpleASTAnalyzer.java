package com.deepcode.astplugin;

import com.intellij.lang.Language;
import com.intellij.psi.*;
import com.intellij.psi.util.PsiTreeUtil;

import java.util.*;

public class SimpleASTAnalyzer {
    private final StringBuilder report = new StringBuilder();
    private int indentLevel = 0;
    private final Map<String, Integer> statistics = new HashMap<>();

    public String generateASTReport(PsiFile psiFile) {
        report.setLength(0);
        indentLevel = 0;
        statistics.clear();

        try {
            appendHeader(psiFile);
            
            Language language = psiFile.getLanguage();
            String languageId = language.getID();
            
            if ("JAVA".equals(languageId) && psiFile instanceof PsiJavaFile) {
                analyzeJavaFile((PsiJavaFile) psiFile);
            } else {
                analyzeGenericFile(psiFile);
            }
            
            appendStatistics();
            
        } catch (Exception e) {
            appendLine("Error during analysis: " + e.getMessage());
        }
        
        return report.toString();
    }

    private void appendHeader(PsiFile psiFile) {
        appendLine("=== AST Analysis Report ===");
        appendLine("File: " + psiFile.getName());
        appendLine("Language: " + psiFile.getLanguage().getDisplayName());
        appendLine("Virtual File: " + (psiFile.getVirtualFile() != null ? psiFile.getVirtualFile().getPath() : "N/A"));
        appendLine("Timestamp: " + new Date());
        appendLine("");
    }

    private void analyzeJavaFile(PsiJavaFile javaFile) {
        appendSection("Java File Analysis");
        indentLevel++;

        // Package declaration
        String packageName = javaFile.getPackageName();
        if (!packageName.isEmpty()) {
            appendLine("Package: " + packageName);
            updateStatistics("packages");
        }

        // Import statements
        PsiImportList importList = javaFile.getImportList();
        if (importList != null) {
            PsiImportStatement[] imports = importList.getImportStatements();
            if (imports.length > 0) {
                appendLine("Imports (" + imports.length + "):");
                indentLevel++;
                for (PsiImportStatement importStmt : imports) {
                    appendLine("- " + importStmt.getQualifiedName());
                    updateStatistics("imports");
                }
                indentLevel--;
            }
        }

        // Classes
        PsiClass[] classes = javaFile.getClasses();
        if (classes.length > 0) {
            appendLine("Classes (" + classes.length + "):");
            indentLevel++;
            for (PsiClass cls : classes) {
                analyzeJavaClass(cls);
            }
            indentLevel--;
        }

        indentLevel--;
    }

    private void analyzeJavaClass(PsiClass cls) {
        String classType = cls.isInterface() ? "Interface" :
                          cls.isEnum() ? "Enum" :
                          cls.isAnnotationType() ? "Annotation" : "Class";
        
        appendLine(classType + ": " + cls.getName());
        updateStatistics("classes");
        indentLevel++;

        // Modifiers
        PsiModifierList modifiers = cls.getModifierList();
        if (modifiers != null) {
            List<String> modifierList = new ArrayList<>();
            if (modifiers.hasModifierProperty(PsiModifier.PUBLIC)) modifierList.add("public");
            if (modifiers.hasModifierProperty(PsiModifier.PRIVATE)) modifierList.add("private");
            if (modifiers.hasModifierProperty(PsiModifier.PROTECTED)) modifierList.add("protected");
            if (modifiers.hasModifierProperty(PsiModifier.STATIC)) modifierList.add("static");
            if (modifiers.hasModifierProperty(PsiModifier.FINAL)) modifierList.add("final");
            if (modifiers.hasModifierProperty(PsiModifier.ABSTRACT)) modifierList.add("abstract");
            
            if (!modifierList.isEmpty()) {
                appendLine("Modifiers: " + String.join(", ", modifierList));
            }
        }

        // Fields
        PsiField[] fields = cls.getFields();
        if (fields.length > 0) {
            appendLine("Fields (" + fields.length + "):");
            indentLevel++;
            for (PsiField field : fields) {
                analyzeJavaField(field);
            }
            indentLevel--;
        }

        // Methods
        PsiMethod[] methods = cls.getMethods();
        if (methods.length > 0) {
            appendLine("Methods (" + methods.length + "):");
            indentLevel++;
            for (PsiMethod method : methods) {
                analyzeJavaMethod(method);
            }
            indentLevel--;
        }

        // Inner classes
        PsiClass[] innerClasses = cls.getInnerClasses();
        if (innerClasses.length > 0) {
            appendLine("Inner Classes (" + innerClasses.length + "):");
            indentLevel++;
            for (PsiClass innerClass : innerClasses) {
                analyzeJavaClass(innerClass);
            }
            indentLevel--;
        }

        indentLevel--;
    }

    private void analyzeJavaField(PsiField field) {
        String fieldInfo = field.getName() + ": " + field.getType().getPresentableText();
        appendLine(fieldInfo);
        updateStatistics("fields");
    }

    private void analyzeJavaMethod(PsiMethod method) {
        StringBuilder methodInfo = new StringBuilder();
        methodInfo.append(method.getName()).append("(");
        
        PsiParameter[] parameters = method.getParameterList().getParameters();
        for (int i = 0; i < parameters.length; i++) {
            if (i > 0) methodInfo.append(", ");
            methodInfo.append(parameters[i].getType().getPresentableText());
        }
        methodInfo.append(")");
        
        PsiType returnType = method.getReturnType();
        if (returnType != null) {
            methodInfo.append(": ").append(returnType.getPresentableText());
        }
        
        appendLine(methodInfo.toString());
        updateStatistics("methods");
    }

    private void analyzeGenericFile(PsiFile psiFile) {
        appendSection("Generic File Analysis");
        indentLevel++;

        // Basic file info
        appendLine("File size: " + psiFile.getTextLength() + " characters");
        
        // Count different element types
        Collection<PsiElement> allElements = PsiTreeUtil.findChildrenOfType(psiFile, PsiElement.class);
        Map<String, Integer> elementCounts = new HashMap<>();
        
        for (PsiElement element : allElements) {
            String elementType = element.getClass().getSimpleName();
            elementCounts.put(elementType, elementCounts.getOrDefault(elementType, 0) + 1);
        }
        
        appendLine("Total PSI elements: " + allElements.size());
        
        if (!elementCounts.isEmpty()) {
            appendLine("Element types:");
            indentLevel++;
            elementCounts.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(10)
                .forEach(entry -> appendLine(entry.getKey() + ": " + entry.getValue()));
            indentLevel--;
        }

        indentLevel--;
    }

    private void appendStatistics() {
        if (!statistics.isEmpty()) {
            appendLine("");
            appendSection("Statistics");
            indentLevel++;
            statistics.forEach((key, value) -> 
                appendLine(key + ": " + value));
            indentLevel--;
        }
    }

    private void appendSection(String title) {
        appendLine("");
        appendLine("=== " + title + " ===");
    }

    private void appendLine(String line) {
        for (int i = 0; i < indentLevel; i++) {
            report.append("  ");
        }
        report.append(line).append("\n");
    }

    private void updateStatistics(String key) {
        statistics.put(key, statistics.getOrDefault(key, 0) + 1);
    }
}
