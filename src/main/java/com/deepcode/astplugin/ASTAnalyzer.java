package com.deepcode.astplugin;

import com.intellij.lang.Language;
import com.intellij.openapi.project.Project;
import com.intellij.psi.*;
import com.intellij.psi.util.PsiTreeUtil;
import com.intellij.psi.search.GlobalSearchScope;
import com.intellij.psi.search.searches.ReferencesSearch;
import com.intellij.psi.javadoc.PsiDocComment;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.stream.Collectors;

// Python imports
import com.jetbrains.python.psi.*;
import com.jetbrains.python.psi.types.TypeEvalContext;

// Python imports
import com.jetbrains.python.psi.*;

// JavaScript imports
import com.intellij.lang.javascript.psi.*;

public class ASTAnalyzer {
    private final StringBuilder report = new StringBuilder();
    private int indentLevel = 0;
    private final Map<String, Integer> statistics = new HashMap<>();

    public String generateASTReport(PsiFile psiFile) {
        report.setLength(0); // Reset report
        indentLevel = 0;
        statistics.clear();

        appendHeader(psiFile);

        Language language = psiFile.getLanguage();
        String languageId = language.getID();

        try {
            if ("JAVA".equals(languageId)) {
                analyzeJavaFile((PsiJavaFile) psiFile);
            } else if ("Python".equals(languageId)) {
                analyzePythonFile(psiFile);
            } else if ("JavaScript".equals(languageId) || "TypeScript".equals(languageId)) {
                analyzeJavaScriptFile(psiFile);
            } else {
                appendLine("Unsupported language: " + language.getDisplayName());
                appendLine("Language ID: " + languageId);
                // 尝试通用分析
                analyzeGenericFile(psiFile);
            }

            // 添加统计信息
            appendStatistics();

        } catch (Exception e) {
            appendLine("Error during analysis: " + e.getMessage());
            e.printStackTrace();
        }

        return report.toString();
    }

    private void appendHeader(PsiFile psiFile) {
        appendLine("=== Complete AST Analysis Report ===");
        appendLine("File: " + psiFile.getName());
        appendLine("Language: " + psiFile.getLanguage().getDisplayName());
        appendLine("Language ID: " + psiFile.getLanguage().getID());
        appendLine("Project: " + psiFile.getProject().getName());
        appendLine("File Type: " + psiFile.getFileType().getName());
        appendLine("Virtual File: " + (psiFile.getVirtualFile() != null ? psiFile.getVirtualFile().getPath() : "N/A"));
        appendLine("Text Length: " + psiFile.getTextLength() + " characters");
        appendLine("Timestamp: " + new Date());
        appendLine("========================================");
    }

    private void appendStatistics() {
        if (!statistics.isEmpty()) {
            appendLine("");
            appendSection("Analysis Statistics");
            indentLevel++;
            for (Map.Entry<String, Integer> entry : statistics.entrySet()) {
                appendLine(entry.getKey() + ": " + entry.getValue());
            }
            indentLevel--;
        }
    }

    private void incrementStat(String key) {
        statistics.put(key, statistics.getOrDefault(key, 0) + 1);
    }

    private void analyzeGenericFile(PsiFile psiFile) {
        appendSection("Generic PSI Analysis");
        indentLevel++;

        // 分析所有PSI元素
        psiFile.accept(new PsiRecursiveElementVisitor() {
            @Override
            public void visitElement(@NotNull PsiElement element) {
                String elementType = element.getClass().getSimpleName();
                incrementStat("PSI Elements: " + elementType);
                super.visitElement(element);
            }
        });

        // 显示PSI树结构
        appendSection("PSI Tree Structure");
        indentLevel++;
        analyzePsiTree(psiFile, 0, 10); // 限制深度为10
        indentLevel--;

        indentLevel--;
    }

    private void analyzePsiTree(PsiElement element, int currentDepth, int maxDepth) {
        if (currentDepth > maxDepth) {
            appendLine("... (max depth reached)");
            return;
        }

        String elementInfo = element.getClass().getSimpleName();
        if (element instanceof PsiNamedElement) {
            PsiNamedElement named = (PsiNamedElement) element;
            elementInfo += " [" + named.getName() + "]";
        }

        String text = element.getText();
        if (text != null && text.length() > 50) {
            text = text.substring(0, 47) + "...";
        }
        if (text != null && !text.contains("\n")) {
            elementInfo += " \"" + text + "\"";
        }

        appendLine(elementInfo);

        // 递归分析子元素
        indentLevel++;
        for (PsiElement child : element.getChildren()) {
            analyzePsiTree(child, currentDepth + 1, maxDepth);
        }
        indentLevel--;
    }

    private void analyzeJavaFile(PsiJavaFile javaFile) {
        appendSection("Java File Analysis");
        indentLevel++;

        // Package information
        String packageName = javaFile.getPackageName();
        if (!packageName.isEmpty()) {
            appendSection("Package Declaration");
            indentLevel++;
            appendLine("Package: " + packageName);
            PsiPackageStatement packageStatement = javaFile.getPackageStatement();
            if (packageStatement != null) {
                appendLine("Package Statement: " + packageStatement.getText());
            }
            indentLevel--;
            incrementStat("Java Packages");
        }

        // Import analysis
        PsiImportList importList = javaFile.getImportList();
        if (importList != null) {
            PsiImportStatement[] imports = importList.getImportStatements();
            PsiImportStaticStatement[] staticImports = importList.getImportStaticStatements();

            if (imports.length > 0 || staticImports.length > 0) {
                appendSection("Import Statements");
                indentLevel++;

                for (PsiImportStatement imp : imports) {
                    String importText = imp.getQualifiedName();
                    appendLine("Import: " + importText + (imp.isOnDemand() ? ".*" : ""));
                    incrementStat("Java Imports");
                }

                for (PsiImportStaticStatement staticImp : staticImports) {
                    String importText = staticImp.getImportReference() != null ?
                        staticImp.getImportReference().getCanonicalText() : "unknown";
                    appendLine("Static Import: " + importText + (staticImp.isOnDemand() ? ".*" : ""));
                    incrementStat("Java Static Imports");
                }

                indentLevel--;
            }
        }

        // Class analysis
        PsiClass[] classes = javaFile.getClasses();
        if (classes.length > 0) {
            appendSection("Classes and Interfaces");
            indentLevel++;
            for (PsiClass cls : classes) {
                analyzeJavaClass(cls);
            }
            indentLevel--;
        }

        // Module analysis (Java 9+)
        PsiJavaModule module = javaFile.getModuleDeclaration();
        if (module != null) {
            appendSection("Module Declaration");
            indentLevel++;
            appendLine("Module: " + module.getName());
            appendLine("Module Text: " + module.getText());
            indentLevel--;
            incrementStat("Java Modules");
        }

        indentLevel--;
    }

    private void analyzeJavaClass(PsiClass cls) {
        String classType = cls.isInterface() ? "Interface" :
                          cls.isEnum() ? "Enum" :
                          cls.isAnnotationType() ? "Annotation" : "Class";

        appendSection(classType + ": " + cls.getName());
        indentLevel++;
        incrementStat("Java " + classType + "s");

        try {
            // Basic information
            appendLine("Qualified Name: " + (cls.getQualifiedName() != null ? cls.getQualifiedName() : "N/A"));
            appendLine("Is Abstract: " + cls.hasModifierProperty(PsiModifier.ABSTRACT));
            appendLine("Is Final: " + cls.hasModifierProperty(PsiModifier.FINAL));
            appendLine("Is Static: " + cls.hasModifierProperty(PsiModifier.STATIC));

            // Modifiers
            if (cls.getModifierList() != null) {
                PsiModifierList modifiers = cls.getModifierList();
                List<String> modifierList = new ArrayList<>();
                for (String modifier : PsiModifier.MODIFIERS) {
                    if (modifiers.hasModifierProperty(modifier)) {
                        modifierList.add(modifier);
                    }
                }
                appendLine("Modifiers: " + String.join(", ", modifierList));
            }

            // Inheritance
            PsiClass superClass = cls.getSuperClass();
            if (superClass != null) {
                appendLine("Extends: " + superClass.getQualifiedName());
                incrementStat("Java Inheritance Relations");
            }

            // Interfaces
            PsiClass[] interfaces = cls.getInterfaces();
            if (interfaces.length > 0) {
                appendSection("Implements/Extends Interfaces");
                indentLevel++;
                for (PsiClass interfaceClass : interfaces) {
                    appendLine(interfaceClass.getQualifiedName());
                    incrementStat("Java Interface Implementations");
                }
                indentLevel--;
            }

            // Type parameters (generics)
            PsiTypeParameter[] typeParameters = cls.getTypeParameters();
            if (typeParameters.length > 0) {
                appendSection("Generic Type Parameters");
                indentLevel++;
                for (PsiTypeParameter typeParam : typeParameters) {
                    appendLine("Type Parameter: " + typeParam.getName());
                    PsiClassType[] bounds = typeParam.getExtendsList().getReferencedTypes();
                    if (bounds.length > 0) {
                        appendLine("  Bounds: " + Arrays.stream(bounds)
                            .map(PsiType::getPresentableText)
                            .collect(Collectors.joining(", ")));
                    }
                    incrementStat("Java Generic Type Parameters");
                }
                indentLevel--;
            }

            // Annotations
            PsiAnnotation[] annotations = cls.getAnnotations();
            if (annotations.length > 0) {
                appendSection("Annotations");
                indentLevel++;
                for (PsiAnnotation annotation : annotations) {
                    appendLine("@" + annotation.getQualifiedName());
                    incrementStat("Java Annotations");
                }
                indentLevel--;
            }

            // Fields analysis
            PsiField[] fields = cls.getFields();
            if (fields.length > 0) {
                appendSection("Fields");
                indentLevel++;
                for (PsiField field : fields) {
                    analyzeJavaField(field);
                }
                indentLevel--;
            }

            // Methods analysis
            PsiMethod[] methods = cls.getMethods();
            if (methods.length > 0) {
                appendSection("Methods");
                indentLevel++;
                for (PsiMethod method : methods) {
                    analyzeJavaMethod(method);
                }
                indentLevel--;
            }

            // Inner classes
            PsiClass[] innerClasses = cls.getInnerClasses();
            if (innerClasses.length > 0) {
                appendSection("Inner Classes");
                indentLevel++;
                for (PsiClass innerClass : innerClasses) {
                    analyzeJavaClass(innerClass); // Recursive analysis
                }
                indentLevel--;
            }

            // Javadoc
            PsiDocComment docComment = cls.getDocComment();
            if (docComment != null) {
                appendSection("Documentation");
                indentLevel++;
                appendLine("Javadoc: " + docComment.getText().replaceAll("\\s+", " ").substring(0,
                    Math.min(100, docComment.getText().length())) + "...");
                indentLevel--;
                incrementStat("Java Documented Classes");
            }

        } catch (Exception e) {
            appendLine("Error analyzing class: " + e.getMessage());
        }

        indentLevel--;
    }

    private void analyzeJavaField(PsiField field) {
        try {
            String fieldInfo = field.getType().getPresentableText() + " " + field.getName();

            // Add modifiers
            List<String> modifiers = new ArrayList<>();
            if (field.hasModifierProperty(PsiModifier.PUBLIC)) modifiers.add("public");
            if (field.hasModifierProperty(PsiModifier.PRIVATE)) modifiers.add("private");
            if (field.hasModifierProperty(PsiModifier.PROTECTED)) modifiers.add("protected");
            if (field.hasModifierProperty(PsiModifier.STATIC)) modifiers.add("static");
            if (field.hasModifierProperty(PsiModifier.FINAL)) modifiers.add("final");
            if (field.hasModifierProperty(PsiModifier.VOLATILE)) modifiers.add("volatile");
            if (field.hasModifierProperty(PsiModifier.TRANSIENT)) modifiers.add("transient");

            if (!modifiers.isEmpty()) {
                fieldInfo = String.join(" ", modifiers) + " " + fieldInfo;
            }

            // Add initializer if present
            PsiExpression initializer = field.getInitializer();
            if (initializer != null) {
                String initText = initializer.getText();
                if (initText.length() > 30) {
                    initText = initText.substring(0, 27) + "...";
                }
                fieldInfo += " = " + initText;
            }

            appendLine(fieldInfo);
            incrementStat("Java Fields");

            // Annotations on field
            PsiAnnotation[] annotations = field.getAnnotations();
            if (annotations.length > 0) {
                indentLevel++;
                for (PsiAnnotation annotation : annotations) {
                    appendLine("@" + annotation.getQualifiedName());
                }
                indentLevel--;
            }

        } catch (Exception e) {
            appendLine("Field: " + field.getName() + " (analysis failed: " + e.getMessage() + ")");
        }
    }

    private void analyzeJavaMethod(PsiMethod method) {
        try {
            String methodSignature = method.getName();

            // Constructor vs method
            boolean isConstructor = method.isConstructor();
            appendSection((isConstructor ? "Constructor: " : "Method: ") + methodSignature + "()");
            indentLevel++;
            incrementStat("Java " + (isConstructor ? "Constructors" : "Methods"));

            // Return type
            PsiType returnType = method.getReturnType();
            if (returnType != null) {
                appendLine("Return Type: " + returnType.getPresentableText());
            } else if (!isConstructor) {
                appendLine("Return Type: void");
            }

            // Modifiers
            List<String> modifiers = new ArrayList<>();
            if (method.hasModifierProperty(PsiModifier.PUBLIC)) modifiers.add("public");
            if (method.hasModifierProperty(PsiModifier.PRIVATE)) modifiers.add("private");
            if (method.hasModifierProperty(PsiModifier.PROTECTED)) modifiers.add("protected");
            if (method.hasModifierProperty(PsiModifier.STATIC)) modifiers.add("static");
            if (method.hasModifierProperty(PsiModifier.FINAL)) modifiers.add("final");
            if (method.hasModifierProperty(PsiModifier.ABSTRACT)) modifiers.add("abstract");
            if (method.hasModifierProperty(PsiModifier.SYNCHRONIZED)) modifiers.add("synchronized");
            if (method.hasModifierProperty(PsiModifier.NATIVE)) modifiers.add("native");

            if (!modifiers.isEmpty()) {
                appendLine("Modifiers: " + String.join(", ", modifiers));
            }

            // Parameters
            PsiParameter[] parameters = method.getParameterList().getParameters();
            if (parameters.length > 0) {
                appendSection("Parameters");
                indentLevel++;
                for (PsiParameter param : parameters) {
                    String paramInfo = param.getType().getPresentableText() + " " + param.getName();
                    if (param.isVarArgs()) {
                        paramInfo += " (varargs)";
                    }
                    appendLine(paramInfo);
                    incrementStat("Java Method Parameters");
                }
                indentLevel--;
            }

            // Exceptions
            PsiClassType[] exceptions = method.getThrowsList().getReferencedTypes();
            if (exceptions.length > 0) {
                appendLine("Throws: " + Arrays.stream(exceptions)
                    .map(PsiType::getPresentableText)
                    .collect(Collectors.joining(", ")));
            }

            // Annotations
            PsiAnnotation[] annotations = method.getAnnotations();
            if (annotations.length > 0) {
                appendSection("Annotations");
                indentLevel++;
                for (PsiAnnotation annotation : annotations) {
                    appendLine("@" + annotation.getQualifiedName());
                }
                indentLevel--;
            }

            // Method body analysis
            PsiCodeBlock body = method.getBody();
            if (body != null) {
                analyzeMethodBody(body);
            } else {
                appendLine("Abstract method (no body)");
            }

        } catch (Exception e) {
            appendLine("Error analyzing method: " + e.getMessage());
        }

        indentLevel--;
    }

    private void analyzeMethodBody(PsiCodeBlock body) {
        appendSection("Method Body Analysis");
        indentLevel++;

        // Count statements
        PsiStatement[] statements = body.getStatements();
        appendLine("Statement Count: " + statements.length);

        // Method calls
        Collection<PsiMethodCallExpression> methodCalls =
            PsiTreeUtil.findChildrenOfType(body, PsiMethodCallExpression.class);
        if (!methodCalls.isEmpty()) {
            appendLine("Method Calls: " + methodCalls.size());
            indentLevel++;
            Set<String> uniqueCalls = new HashSet<>();
            for (PsiMethodCallExpression call : methodCalls) {
                PsiMethod resolvedMethod = call.resolveMethod();
                if (resolvedMethod != null) {
                    String callInfo = resolvedMethod.getContainingClass() != null ?
                        resolvedMethod.getContainingClass().getName() + "." + resolvedMethod.getName() :
                        resolvedMethod.getName();
                    uniqueCalls.add(callInfo);
                }
            }
            for (String call : uniqueCalls) {
                appendLine(call + "()");
            }
            indentLevel--;
            incrementStat("Java Method Calls");
        }

        // Variable declarations
        Collection<PsiDeclarationStatement> declarations =
            PsiTreeUtil.findChildrenOfType(body, PsiDeclarationStatement.class);
        if (!declarations.isEmpty()) {
            appendLine("Local Variables: " + declarations.size());
            incrementStat("Java Local Variables");
        }

        // Control flow statements
        Collection<PsiIfStatement> ifStatements = PsiTreeUtil.findChildrenOfType(body, PsiIfStatement.class);
        Collection<PsiForStatement> forStatements = PsiTreeUtil.findChildrenOfType(body, PsiForStatement.class);
        Collection<PsiWhileStatement> whileStatements = PsiTreeUtil.findChildrenOfType(body, PsiWhileStatement.class);
        Collection<PsiTryStatement> tryStatements = PsiTreeUtil.findChildrenOfType(body, PsiTryStatement.class);

        if (!ifStatements.isEmpty()) appendLine("If Statements: " + ifStatements.size());
        if (!forStatements.isEmpty()) appendLine("For Loops: " + forStatements.size());
        if (!whileStatements.isEmpty()) appendLine("While Loops: " + whileStatements.size());
        if (!tryStatements.isEmpty()) appendLine("Try-Catch Blocks: " + tryStatements.size());

        indentLevel--;
    }

    private void analyzePythonFile(PsiFile psiFile) {
        appendSection("Python Analysis");
        indentLevel++;

        // Analyze classes
        PyClass[] classes = PsiTreeUtil.findChildrenOfType(psiFile, PyClass.class);
        if (classes.length > 0) {
            appendSection("Classes");
            indentLevel++;
            for (PyClass cls : classes) {
                appendLine("Class: " + cls.getName());
                indentLevel++;
                appendLine("Bases: " + String.join(", ", cls.getSuperClassNames()));

                // Analyze class methods
                PyFunction[] methods = cls.getMethods();
                if (methods.length > 0) {
                    appendLine("Methods:");
                    indentLevel++;
                    for (PyFunction method : methods) {
                        appendLine(method.getName() + "(" + getPyParameters(method) + ")");
                    }
                    indentLevel--;
                }
                indentLevel--;
            }
            indentLevel--;
        }

        // Analyze top-level functions
        PyFunction[] functions = PsiTreeUtil.findChildrenOfType(psiFile, PyFunction.class);
        List<PyFunction> topLevelFunctions = new ArrayList<>();
        for (PyFunction func : functions) {
            if (func.getContainingClass() == null) {
                topLevelFunctions.add(func);
            }
        }

        if (!topLevelFunctions.isEmpty()) {
            appendSection("Top-level Functions");
            indentLevel++;
            for (PyFunction func : topLevelFunctions) {
                appendLine(func.getName() + "(" + getPyParameters(func) + ")");
            }
            indentLevel--;
        }

        indentLevel--;
    }

    private String getPyParameters(PyFunction function) {
        return java.util.Arrays.stream(function.getParameterList().getParameters())
                .map(param -> param.getName())
                .collect(Collectors.joining(", "));
    }

    private void analyzeJavaScriptFile(PsiFile psiFile) {
        appendSection("JavaScript Analysis");
        indentLevel++;

        // Analyze functions
        JSFunction[] functions = PsiTreeUtil.findChildrenOfType(psiFile, JSFunction.class);
        if (functions.length > 0) {
            appendSection("Functions");
            indentLevel++;
            for (JSFunction func : functions) {
                appendLine(func.getName() + "(" + getJSParameters(func) + ")");
            }
            indentLevel--;
        }

        // Analyze variables
        JSVariable[] variables = PsiTreeUtil.findChildrenOfType(psiFile, JSVariable.class);
        if (variables.length > 0) {
            appendSection("Variables");
            indentLevel++;
            for (JSVariable var : variables) {
                String type = var.getType() != null ? var.getType().getText() : "any";
                appendLine(type + " " + var.getName());
            }
            indentLevel--;
        }

        indentLevel--;
    }

    private String getJSParameters(JSFunction function) {
        return java.util.Arrays.stream(function.getParameterList().getParameters())
                .map(param -> param.getName())
                .collect(Collectors.joining(", "));
    }

    private void appendSection(String title) {
        appendLine("");
        appendLine(title + ":");
    }

    private void appendSection(String title, String content) {
        appendLine("");
        appendLine(title + ": " + content);
    }

    private void appendLine(String text) {
        report.append("    ".repeat(indentLevel)).append(text).append("\n");
    }
}