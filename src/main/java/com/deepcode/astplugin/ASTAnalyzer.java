package com.example.astplugin;

import com.intellij.lang.Language;
import com.intellij.openapi.project.Project;
import com.intellij.psi.*;
import com.intellij.psi.util.PsiTreeUtil;
import org.jetbrains.annotations.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import com.jetbrains.python.psi.PyClass;
import com.jetbrains.python.psi.PyFunction;
import com.intellij.lang.javascript.psi.JSFunction;
import com.intellij.lang.javascript.psi.JSVariable;

public class ASTAnalyzer {
    private final StringBuilder report = new StringBuilder();
    private int indentLevel = 0;

    public String generateASTReport(PsiFile psiFile) {
        report.setLength(0); // Reset report
        indentLevel = 0;

        appendHeader(psiFile);

        Language language = psiFile.getLanguage();
        if (language.getID().equals("JAVA")) {
            analyzeJavaFile((PsiJavaFile) psiFile);
        } else if (language.getID().equals("Python")) {
            analyzePythonFile(psiFile);
        } else if (language.getID().equals("JavaScript")) {
            analyzeJavaScriptFile(psiFile);
        } else {
            appendLine("Unsupported language: " + language.getDisplayName());
        }

        return report.toString();
    }

    private void appendHeader(PsiFile psiFile) {
        appendLine("=== AST Report ===");
        appendLine("File: " + psiFile.getName());
        appendLine("Language: " + psiFile.getLanguage().getDisplayName());
        appendLine("Project: " + psiFile.getProject().getName());
        appendLine("========================================");
    }

    private void analyzeJavaFile(PsiJavaFile javaFile) {
        appendSection("Package", javaFile.getPackageName());

        // Analyze imports
        PsiImportStatement[] imports = javaFile.getImportStatements();
        if (imports.length > 0) {
            appendSection("Imports");
            indentLevel++;
            for (PsiImportStatement imp : imports) {
                appendLine(imp.getText().replace(";", ""));
            }
            indentLevel--;
        }

        // Analyze classes
        PsiClass[] classes = javaFile.getClasses();
        for (PsiClass cls : classes) {
            analyzeJavaClass(cls);
        }
    }

    private void analyzeJavaClass(PsiClass cls) {
        appendSection("Class: " + cls.getName());
        indentLevel++;

        // Class modifiers and supertypes
        appendLine("Modifiers: " + String.join(", ", cls.getModifierList().getModifiers()));
        appendLine("Superclass: " + (cls.getSuperClass() != null ? cls.getSuperClass().getQualifiedName() : "none"));
        appendLine("Interfaces: " + String.join(", ",
                cls.getInterfaces().stream()
                        .map(PsiClass::getQualifiedName)
                        .collect(Collectors.toList())));

        // Analyze fields
        PsiField[] fields = cls.getFields();
        if (fields.length > 0) {
            appendSection("Fields");
            indentLevel++;
            for (PsiField field : fields) {
                appendLine(field.getType().getPresentableText() + " " + field.getName() + ";");
            }
            indentLevel--;
        }

        // Analyze methods
        PsiMethod[] methods = cls.getMethods();
        for (PsiMethod method : methods) {
            analyzeJavaMethod(method);
        }

        indentLevel--;
    }

    private void analyzeJavaMethod(PsiMethod method) {
        appendSection("Method: " + method.getName() + "()");
        indentLevel++;

        // Method signature
        appendLine("ReturnType: " + method.getReturnType().getPresentableText());
        appendLine("Modifiers: " + String.join(", ", method.getModifierList().getModifiers()));

        // Parameters
        PsiParameter[] parameters = method.getParameterList().getParameters();
        if (parameters.length > 0) {
            appendLine("Parameters: " + String.join(", ",
                    java.util.Arrays.stream(parameters)
                            .map(p -> p.getType().getPresentableText() + " " + p.getName())
                            .collect(Collectors.toList())));
        }

        // Method calls
        List<String> methodCalls = new ArrayList<>();
        PsiTreeUtil.findChildrenOfType(method.getBody(), PsiMethodCallExpression.class)
                .forEach(call -> methodCalls.add(call.getMethodExpression().getQualifiedName()));

        if (!methodCalls.isEmpty()) {
            appendLine("Calls: " + String.join(", ", methodCalls));
        }

        indentLevel--;
    }

    private void analyzePythonFile(PsiFile psiFile) {
        appendSection("Python Analysis");
        indentLevel++;

        // Analyze classes
        PyClass[] classes = PsiTreeUtil.findChildrenOfType(psiFile, PyClass.class);
        if (classes.length > 0) {
            appendSection("Classes");
            indentLevel++;
            for (PyClass cls : classes) {
                appendLine("Class: " + cls.getName());
                indentLevel++;
                appendLine("Bases: " + String.join(", ", cls.getSuperClassNames()));

                // Analyze class methods
                PyFunction[] methods = cls.getMethods();
                if (methods.length > 0) {
                    appendLine("Methods:");
                    indentLevel++;
                    for (PyFunction method : methods) {
                        appendLine(method.getName() + "(" + getPyParameters(method) + ")");
                    }
                    indentLevel--;
                }
                indentLevel--;
            }
            indentLevel--;
        }

        // Analyze top-level functions
        PyFunction[] functions = PsiTreeUtil.findChildrenOfType(psiFile, PyFunction.class);
        List<PyFunction> topLevelFunctions = new ArrayList<>();
        for (PyFunction func : functions) {
            if (func.getContainingClass() == null) {
                topLevelFunctions.add(func);
            }
        }

        if (!topLevelFunctions.isEmpty()) {
            appendSection("Top-level Functions");
            indentLevel++;
            for (PyFunction func : topLevelFunctions) {
                appendLine(func.getName() + "(" + getPyParameters(func) + ")");
            }
            indentLevel--;
        }

        indentLevel--;
    }

    private String getPyParameters(PyFunction function) {
        return java.util.Arrays.stream(function.getParameterList().getParameters())
                .map(param -> param.getName())
                .collect(Collectors.joining(", "));
    }

    private void analyzeJavaScriptFile(PsiFile psiFile) {
        appendSection("JavaScript Analysis");
        indentLevel++;

        // Analyze functions
        JSFunction[] functions = PsiTreeUtil.findChildrenOfType(psiFile, JSFunction.class);
        if (functions.length > 0) {
            appendSection("Functions");
            indentLevel++;
            for (JSFunction func : functions) {
                appendLine(func.getName() + "(" + getJSParameters(func) + ")");
            }
            indentLevel--;
        }

        // Analyze variables
        JSVariable[] variables = PsiTreeUtil.findChildrenOfType(psiFile, JSVariable.class);
        if (variables.length > 0) {
            appendSection("Variables");
            indentLevel++;
            for (JSVariable var : variables) {
                String type = var.getType() != null ? var.getType().getText() : "any";
                appendLine(type + " " + var.getName());
            }
            indentLevel--;
        }

        indentLevel--;
    }

    private String getJSParameters(JSFunction function) {
        return java.util.Arrays.stream(function.getParameterList().getParameters())
                .map(param -> param.getName())
                .collect(Collectors.joining(", "));
    }

    private void appendSection(String title) {
        appendLine("");
        appendLine(title + ":");
    }

    private void appendSection(String title, String content) {
        appendLine("");
        appendLine(title + ": " + content);
    }

    private void appendLine(String text) {
        report.append("    ".repeat(indentLevel)).append(text).append("\n");
    }
}