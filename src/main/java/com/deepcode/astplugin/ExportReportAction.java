package com.deepcode.astplugin;

import com.intellij.openapi.actionSystem.AnAction;
import com.intellij.openapi.actionSystem.AnActionEvent;
import com.intellij.openapi.actionSystem.CommonDataKeys;
import com.intellij.openapi.fileChooser.FileChooser;
import com.intellij.openapi.fileChooser.FileChooserDescriptor;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.ui.Messages;
import com.intellij.openapi.vfs.VirtualFile;
import com.intellij.psi.PsiFile;

import javax.swing.*;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class ExportReportAction extends AnAction {

    @Override
    public void actionPerformed(AnActionEvent e) {
        Project project = e.getProject();
        PsiFile psiFile = e.getData(CommonDataKeys.PSI_FILE);

        if (project == null || psiFile == null) {
            Messages.showErrorDialog("No file selected or project context missing", "Error");
            return;
        }

        // Generate AST report
        ASTAnalyzer analyzer = new ASTAnalyzer();
        String report = analyzer.generateASTReport(psiFile);

        // Show file chooser dialog
        FileChooserDescriptor descriptor = new FileChooserDescriptor(false, true, false, false, false, false);
        descriptor.setTitle("Choose Directory to Save AST Report");
        descriptor.setDescription("Select a directory where the AST report will be saved");

        VirtualFile selectedDir = FileChooser.chooseFile(descriptor, project, null);
        if (selectedDir != null) {
            exportReport(report, selectedDir, psiFile.getName(), project);
        }
    }

    private void exportReport(String report, VirtualFile directory, String originalFileName, Project project) {
        try {
            // Generate filename with timestamp
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String baseFileName = originalFileName.replaceAll("\\.[^.]*$", ""); // Remove extension
            String exportFileName = String.format("AST_Report_%s_%s.txt", baseFileName, timestamp);
            
            String filePath = directory.getPath() + "/" + exportFileName;
            
            // Write report to file
            try (FileWriter writer = new FileWriter(filePath)) {
                writer.write("=== AST ANALYSIS REPORT ===\n");
                writer.write("Generated on: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n");
                writer.write("Original file: " + originalFileName + "\n");
                writer.write("Export location: " + filePath + "\n");
                writer.write("\n" + "=".repeat(50) + "\n\n");
                writer.write(report);
            }
            
            // Show success message
            int result = Messages.showYesNoDialog(
                project,
                "AST report exported successfully to:\n" + filePath + "\n\nWould you like to open the containing folder?",
                "Export Successful",
                "Open Folder",
                "Close",
                Messages.getInformationIcon()
            );
            
            if (result == Messages.YES) {
                try {
                    // Open the containing folder
                    String os = System.getProperty("os.name").toLowerCase();
                    if (os.contains("win")) {
                        Runtime.getRuntime().exec("explorer.exe /select," + filePath);
                    } else if (os.contains("mac")) {
                        Runtime.getRuntime().exec("open -R " + filePath);
                    } else {
                        Runtime.getRuntime().exec("xdg-open " + directory.getPath());
                    }
                } catch (IOException ex) {
                    Messages.showWarningDialog("Could not open folder: " + ex.getMessage(), "Warning");
                }
            }
            
        } catch (IOException ex) {
            Messages.showErrorDialog("Failed to export report: " + ex.getMessage(), "Export Error");
        }
    }

    @Override
    public void update(AnActionEvent e) {
        // Enable action only when a file is open
        PsiFile psiFile = e.getData(CommonDataKeys.PSI_FILE);
        e.getPresentation().setEnabled(psiFile != null);
    }
}
