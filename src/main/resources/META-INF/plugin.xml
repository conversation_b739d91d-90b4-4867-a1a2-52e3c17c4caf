<idea-plugin>
    <id>com.deepcode.astplugin</id>
    <name>AST Analysis Plugin</name>
    <version>1.0</version>
    <vendor email="<EMAIL>" url="http://www.deepcode.com">DeepCode</vendor>

    <description><![CDATA[
        A comprehensive plugin to generate structured AST reports using JetBrains PSI API.
        Supports Java, Python, and JavaScript files with detailed analysis of classes, methods, functions, and variables.
        Features include:
        - Detailed AST structure analysis
        - Export functionality for reports
        - Support for multiple programming languages
        - Easy-to-use interface with keyboard shortcuts
    ]]></description>

    <change-notes><![CDATA[
        Version 1.0:
        - Initial version with basic AST extraction for Java, Python and JavaScript
        - Support for classes, methods, functions, and variables analysis
        - Export functionality for generated reports
        - Keyboard shortcut support (Ctrl+Alt+A)
    ]]></change-notes>

    <idea-version since-build="232" until-build="242.*" />

    <depends>com.intellij.modules.platform</depends>
    <depends>com.intellij.java</depends>
    <depends>Pythonid</depends>
    <depends>JavaScript</depends>

    <extensions defaultExtensionNs="com.intellij">
        <!-- Extension points will be added here later -->
    </extensions>

    <actions>
        <action id="ASTReport.GenerateReport" class="com.deepcode.astplugin.ASTReportAction" text="Generate AST Report" description="Generate structured AST report for the current file">
            <add-to-group group-id="Code" anchor="last" />
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt A" />
        </action>
        <action id="ASTReport.ExportReport" class="com.deepcode.astplugin.ExportReportAction" text="Export AST Report" description="Export AST report to file">
            <add-to-group group-id="Code" anchor="last" />
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt E" />
        </action>
    </actions>
</idea-plugin>
