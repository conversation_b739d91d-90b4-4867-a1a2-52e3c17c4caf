<idea-plugin>
    <id>com.example.astplugin</id>
    <name>AST Analysis Plugin</name>
    <version>1.0</version>
    <vendor email="<EMAIL>" url="http://www.example.com">Example Corp</vendor>

    <description><![CDATA[
        A plugin to generate structured AST reports using JetBrains PSI API.
    ]]></description>

    <change-notes><![CDATA[
        Initial version with basic AST extraction for Java, Python and JavaScript.
    ]]></change-notes>

    <idea-version since-build="231" until-build="241.*" />

    <depends>com.intellij.modules.platform</depends>
    <depends>com.intellij.java</depends>
    <depends>Pythonid</depends>
    <depends>JavaScript</depends>

    <extensions defaultExtensionNs="com.intellij">
        <!-- Extension points will be added here later -->
    </extensions>

    <actions>
        <action id="ASTReport.GenerateReport" class="com.example.astplugin.ASTReportAction" text="Generate AST Report" description="Generate structured AST report">
            <add-to-group group-id="Code" anchor="last" />
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl alt A" />
        </action>
    </actions>
</idea-plugin>