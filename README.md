# AST Analysis Plugin

一个功能强大的代码分析工具，提供两种使用方式：
1. **IntelliJ IDEA插件版本** - 完整的IDE集成体验
2. **独立命令行版本** - 无需IDE，直接分析代码文件

## 功能特性

- **多语言支持**: 支持Java、Python和JavaScript文件的AST分析
- **详细分析**: 提供类、方法、函数、变量的详细结构信息
- **多种使用方式**: IDE插件或独立命令行工具
- **导出功能**: 可将分析报告导出为文本文件
- **用户友好**: 简洁的界面和便捷的操作方式
- **跨平台**: 支持Windows、macOS和Linux

## 快速开始 - 独立版本

### 系统要求
- Java 8 或更高版本

### 立即使用
```bash
# 下载并使用预编译的JAR文件
java -jar simple-ast-analyzer.jar <文件路径>

# 示例
java -jar simple-ast-analyzer.jar examples/SampleJavaFile.java
```

## IntelliJ插件版本要求

- IntelliJ IDEA 2021.3 或更高版本
- Java 11 或更高版本
- 支持的语言插件：
  - Java (内置)
  - Python Plugin
  - JavaScript Plugin

## 编译指南

### 前置条件

1. 安装Java 17或更高版本
2. 安装Git
3. 确保网络连接正常（用于下载依赖）

### 编译步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd demo_plugins
   ```

2. **编译项目**
   ```bash
   # 在项目根目录执行
   ./gradlew build
   ```

3. **生成插件包**
   ```bash
   ./gradlew buildPlugin
   ```
   
   编译成功后，插件包将生成在 `build/distributions/` 目录下，文件名为 `AST Analysis Plugin-1.0-SNAPSHOT.zip`

### 编译问题排查

如果遇到编译问题，请尝试：

1. **清理项目**
   ```bash
   ./gradlew clean
   ```

2. **重新下载依赖**
   ```bash
   ./gradlew --refresh-dependencies build
   ```

3. **检查Java版本**
   ```bash
   java -version
   ```

## 安装指南

### 方法一：从源码安装

1. 按照上述编译指南生成插件包
2. 打开IntelliJ IDEA
3. 进入 `File` → `Settings` → `Plugins`
4. 点击齿轮图标，选择 `Install Plugin from Disk...`
5. 选择生成的 `.zip` 文件
6. 重启IntelliJ IDEA

### 方法二：开发模式运行

1. 在项目根目录执行：
   ```bash
   ./gradlew runIde
   ```
2. 这将启动一个包含插件的IntelliJ IDEA实例

## 使用手册

### 基本使用

1. **生成AST报告**
   - 在编辑器中打开Java、Python或JavaScript文件
   - 使用快捷键 `Ctrl+Alt+A` 或通过菜单 `Code` → `Generate AST Report`
   - 报告将在弹出窗口中显示

2. **导出AST报告**
   - 在编辑器中打开要分析的文件
   - 使用快捷键 `Ctrl+Alt+E` 或通过菜单 `Code` → `Export AST Report`
   - 选择保存目录
   - 报告将以时间戳命名的文本文件形式保存

### 支持的文件类型

#### Java文件分析
- 包信息
- 导入语句
- 类定义（包括修饰符、继承关系）
- 字段信息
- 方法签名和调用关系

#### Python文件分析
- 类定义和继承关系
- 类方法
- 顶级函数
- 函数参数信息

#### JavaScript文件分析
- 函数定义
- 变量声明
- 函数参数信息

### 报告格式示例

```
=== AST Report ===
File: Example.java
Language: Java
Project: MyProject
========================================

Package: com.example

Imports:
    import java.util.List
    import java.util.ArrayList

Class: Example
    Modifiers: public
    Superclass: none
    Interfaces: none
    
    Fields:
        List<String> items;
    
    Method: getItems()
        ReturnType: List<String>
        Modifiers: public
        
    Method: addItem()
        ReturnType: void
        Modifiers: public
        Parameters: String item
```

## 开发指南

### 项目结构

```
src/
├── main/
│   ├── java/com/deepcode/astplugin/
│   │   ├── ASTReportAction.java      # 主要的报告生成动作
│   │   ├── ASTAnalyzer.java          # AST分析核心逻辑
│   │   └── ExportReportAction.java   # 导出功能实现
│   └── resources/
│       └── META-INF/
│           └── plugin.xml            # 插件配置文件
├── build.gradle.kts                  # 构建配置
└── README.md                         # 项目文档
```

### 扩展功能

要添加新的语言支持：

1. 在 `ASTAnalyzer.java` 中添加新的分析方法
2. 在 `generateASTReport` 方法中添加语言检测逻辑
3. 在 `plugin.xml` 中添加相应的依赖

### 调试

1. 使用 `./gradlew runIde` 启动调试实例
2. 在代码中设置断点
3. 使用IntelliJ IDEA的调试功能

## 故障排除

### 常见问题

1. **插件无法加载**
   - 检查IntelliJ IDEA版本是否兼容
   - 确认所需的语言插件已安装

2. **分析结果为空**
   - 确认文件类型是否受支持
   - 检查文件是否包含有效的代码结构

3. **导出失败**
   - 检查目标目录的写入权限
   - 确认磁盘空间充足

### 日志查看

在IntelliJ IDEA中查看日志：
1. `Help` → `Show Log in Explorer/Finder`
2. 查看 `idea.log` 文件中的错误信息

## 贡献指南

欢迎提交问题报告和功能请求！

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 联系方式

- 邮箱: <EMAIL>
- 网站: http://www.deepcode.com

---

**版本**: 1.0  
**最后更新**: 2024年6月
